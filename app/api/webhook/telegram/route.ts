import { NextRequest, NextResponse } from "next/server";
import { <PERSON><PERSON>, webhookCallback } from "grammy";
import { checkGenerationAllowed, decrementBothLimits, getUserLimits, getGlobalSettings, logGeneration } from "@/lib/bot/utils/supabase";
import { generateImage } from "@/lib/bot/utils/openai";

console.log("🔧 Initializing Telegram webhook endpoint...");

// Create bot instance
const bot = new Bot(process.env.TELEGRAM_BOT_TOKEN!);

console.log("🤖 Setting up bot commands...");

// Start command
bot.command("start", (ctx) => {
  console.log(`📱 Start command from user ${ctx.from?.id}`);
  ctx.reply(
    "🎨 Welcome to the CryBaby AI Image Bot!\n\n" +
    "**How to use:**\n" +
    "📸 **Send any photo** → Transform it to CryBaby style\n" +
    "✏️ **/generate [text]** → Create from description\n\n" +
    "**Commands:**\n" +
    "• /generate [prompt] - Generate an image from text\n" +
    "• /limit - Check remaining generations\n" +
    "• /help - Show this help message\n\n" +
    "Try sending me a photo or use:\n" +
    "/generate a cat wearing sunglasses"
  );
});

// Help command
bot.command("help", (ctx) => {
  console.log(`❓ Help command from user ${ctx.from?.id}`);
  ctx.reply(
    "🤖 CryBaby AI Image Bot Help\n\n" +
    "**Two ways to create images:**\n\n" +
    "📸 **Direct Photo Transformation**\n" +
    "• Send any photo → Instant CryBaby style transformation\n" +
    "• Perfect for selfies, landscapes, objects\n\n" +
    "✏️ **Text-to-Image Generation**\n" +
    "• /generate [description] → Create from scratch\n" +
    "• Examples:\n" +
    "  - /generate a sunset over mountains\n" +
    "  - /generate a futuristic city\n" +
    "  - /generate a cute robot\n\n" +
    "**Other Commands:**\n" +
    "• /limit - Check remaining daily generations\n" +
    "• /help - Show this help message\n\n" +
    "**Features:**\n" +
    "• AI-powered by DALL-E 3\n" +
    "• Retro cartoon style with vintage palette\n" +
    "• Automatic CryBaby logo watermarking\n" +
    "• Daily limits: Personal & global capacity"
  );
});

// Limit command
bot.command("limit", async (ctx) => {
  const userId = ctx.from?.id;
  console.log(`📊 Limit command from user ${userId}`);
  
  if (!userId) {
    await ctx.reply("❌ Unable to identify user");
    return;
  }

  try {
    const userLimits = await getUserLimits(userId);
    const globalSettings = await getGlobalSettings();
    
    if (!userLimits || !globalSettings) {
      console.error(`❌ Error fetching limits for user ${userId}`);
      await ctx.reply("❌ Error checking your limits. Please try again.");
      return;
    }

    console.log(`✅ Limits fetched for user ${userId}: user=${userLimits.remaining}, global=${globalSettings.remaining}`);

    await ctx.reply(
      `📊 Generation Limits Status\n\n` +
      `👤 Your Personal Limit:\n` +
      `• Daily limit: ${userLimits.daily_limit} images\n` +
      `• Remaining: ${userLimits.remaining} images\n\n` +
      `🌐 Global System Limit:\n` +
      `• Daily capacity: ${globalSettings.daily_limit} images\n` +
      `• Remaining: ${globalSettings.remaining} images\n\n` +
      `• Resets: Daily at midnight UTC\n` +
      `• Both limits must have capacity for generation`
    );
  } catch (error) {
    console.error(`❌ Error in limit command for user ${userId}:`, error);
    await ctx.reply("❌ Error checking your limits. Please try again.");
  }
});

// Generate command
bot.command("generate", async (ctx) => {
  const userId = ctx.from?.id;
  const prompt = ctx.message?.text?.split(" ").slice(1).join(" ");
  
  console.log(`🎨 Generate command from user ${userId} with prompt: "${prompt}"`);
  
  if (!userId) {
    await ctx.reply("❌ Unable to identify user");
    return;
  }

  if (!prompt) {
    await ctx.reply("❌ Please provide a prompt. Example: /generate a cat in space");
    return;
  }

  try {
    // Check both user and global limits
    console.log(`🔍 Checking generation limits for user ${userId}...`);
    const generationCheck = await checkGenerationAllowed(userId);
    if (!generationCheck) {
      console.error(`❌ Error checking limits for user ${userId}`);
      await ctx.reply("❌ Error checking your limits. Please try again.");
      return;
    }

    if (!generationCheck.allowed) {
      let message = "";
      
      if (generationCheck.reason === 'user_limit_exceeded') {
        console.log(`🚫 User limit exceeded for user ${userId}`);
        message = "🚫 Your daily limit exceeded!\n\n" +
          `You've used all ${generationCheck.user_limit} of your daily image generations.\n` +
          `Global system has ${generationCheck.global_remaining} images remaining.\n\n` +
          "Your limit resets daily at midnight UTC.";
      } else if (generationCheck.reason === 'global_limit_exceeded') {
        console.log(`🚫 Global limit exceeded (user ${userId})`);
        message = "🚫 System daily limit exceeded!\n\n" +
          `The global daily capacity of ${generationCheck.global_limit} images has been reached.\n` +
          `You have ${generationCheck.user_remaining} personal generations remaining.\n\n` +
          "The system resets daily at midnight UTC.";
      }
      
      await ctx.reply(message);
      return;
    }

    console.log(`✅ Generation allowed for user ${userId}. Starting image generation...`);

    // Send "generating" message
    const generatingMessage = await ctx.reply("🎨 Generating your image, please wait...");

    // Enhanced prompt with specific art style
    const enhancedPrompt = `Retro cartoon illustration. Thick black outlines, smooth flat shading, limited warm vintage palette (muted oranges, ochres, teal accents). 1950s newspaper comic style, rounded shapes, subtle paper-grain texture. Clean vector aesthetic, high-resolution, simple background with soft abstract swirls in tan. ${prompt}`;
    
    console.log(`🎨 Generating image with enhanced prompt for user ${userId}...`);
    
    // Generate image
    const result = await generateImage(enhancedPrompt);

    if (result.success && result.imageUrl) {
      console.log(`✅ Image generated successfully for user ${userId}: ${result.imageUrl}`);
      
      // Decrement both user and global limits atomically
      const decrementSuccess = await decrementBothLimits(userId);
      
      if (!decrementSuccess) {
        console.error(`❌ Failed to decrement limits for user ${userId}`);
        await ctx.reply("❌ Limits changed while generating. Please try again.");
        return;
      }
      
      console.log(`✅ Limits decremented for user ${userId}`);
      
      // Log successful generation
      await logGeneration(userId, prompt, true, result.imageUrl);
      console.log(`📝 Generation logged for user ${userId}`);

      // Send the image
      await ctx.replyWithPhoto(result.imageUrl, {
        caption: `🎨 Generated: "${prompt}"\n\n` +
          `Your remaining: ${generationCheck.user_remaining - 1} | ` +
          `Global remaining: ${generationCheck.global_remaining - 1}\n\n` +
          `✨ Watermarked with CryBaby logo`
      });

      console.log(`📸 Image sent to user ${userId}`);

      // Delete the "generating" message
      if (ctx.chat) {
        await ctx.api.deleteMessage(ctx.chat.id, generatingMessage.message_id);
      }
    } else {
      console.error(`❌ Image generation failed for user ${userId}:`, result.error);
      
      // Log failed generation (don't decrement on failure)
      await logGeneration(userId, prompt, false, undefined, result.error);

      await ctx.reply(
        `❌ Failed to generate image: ${result.error}\n\n` +
        "Please try again with a different prompt."
      );
    }
  } catch (error) {
    console.error(`❌ Error in generate command for user ${userId}:`, error);
    
    // Log failed generation
    await logGeneration(userId, prompt, false, undefined, "Unexpected error");

    await ctx.reply(
      "❌ An unexpected error occurred while generating your image.\n" +
      "Please try again later."
    );
  }
});

// Handle image messages (direct image transformation)
bot.on("message:photo", async (ctx) => {
  const userId = ctx.from?.id;
  console.log(`📸 Photo received from user ${userId}`);
  
  if (!userId) {
    await ctx.reply("❌ Unable to identify user");
    return;
  }

  try {
    // Check generation limits
    console.log(`🔍 Checking generation limits for user ${userId}...`);
    const generationCheck = await checkGenerationAllowed(userId);
    if (!generationCheck) {
      console.error(`❌ Error checking limits for user ${userId}`);
      await ctx.reply("❌ Error checking your limits. Please try again.");
      return;
    }

    if (!generationCheck.allowed) {
      let message = "";
      
      if (generationCheck.reason === 'user_limit_exceeded') {
        console.log(`🚫 User limit exceeded for user ${userId}`);
        message = "🚫 Your daily limit exceeded!\n\n" +
          `You've used all ${generationCheck.user_limit} of your daily image generations.\n` +
          `Global system has ${generationCheck.global_remaining} images remaining.\n\n` +
          "Your limit resets daily at midnight UTC.";
      } else if (generationCheck.reason === 'global_limit_exceeded') {
        console.log(`🚫 Global limit exceeded (user ${userId})`);
        message = "🚫 System daily limit exceeded!\n\n" +
          `The global daily capacity of ${generationCheck.global_limit} images has been reached.\n` +
          `You have ${generationCheck.user_remaining} personal generations remaining.\n\n` +
          "The system resets daily at midnight UTC.";
      }
      
      await ctx.reply(message);
      return;
    }

    console.log(`✅ Generation allowed for user ${userId}. Processing image...`);

    // Send "processing" message
    const processingMessage = await ctx.reply("🎨 Transforming your image into CryBaby style, please wait...");

    // For now, we'll use a default transformation prompt
    // In the future, you could analyze the image or let users add captions
    const transformationPrompt = "Transform this image into a retro cartoon illustration with thick black outlines, smooth flat shading, limited warm vintage palette (muted oranges, ochres, teal accents). 1950s newspaper comic style, rounded shapes, subtle paper-grain texture. Clean vector aesthetic, high-resolution, simple background with soft abstract swirls in tan.";
    
    console.log(`🎨 Generating transformed image for user ${userId}...`);
    
    // Generate transformed image
    const result = await generateImage(transformationPrompt);

    if (result.success && result.imageUrl) {
      console.log(`✅ Image transformed successfully for user ${userId}: ${result.imageUrl}`);
      
      // Decrement both user and global limits atomically
      const decrementSuccess = await decrementBothLimits(userId);
      
      if (!decrementSuccess) {
        console.error(`❌ Failed to decrement limits for user ${userId}`);
        await ctx.reply("❌ Limits changed while processing. Please try again.");
        return;
      }
      
      console.log(`✅ Limits decremented for user ${userId}`);
      
      // Log successful generation
      await logGeneration(userId, "Direct image transformation", true, result.imageUrl);
      console.log(`📝 Generation logged for user ${userId}`);

      // Send the transformed image
      await ctx.replyWithPhoto(result.imageUrl, {
        caption: `🎨 Transformed to CryBaby style!\n\n` +
          `Your remaining: ${generationCheck.user_remaining - 1} | ` +
          `Global remaining: ${generationCheck.global_remaining - 1}\n\n` +
          `✨ Watermarked with CryBaby logo\n\n` +
          `💡 Tip: Use /generate [description] for custom creations!`
      });

      console.log(`📸 Transformed image sent to user ${userId}`);

      // Delete the "processing" message
      if (ctx.chat) {
        await ctx.api.deleteMessage(ctx.chat.id, processingMessage.message_id);
      }
    } else {
      console.error(`❌ Image transformation failed for user ${userId}:`, result.error);
      
      // Log failed generation (don't decrement on failure)
      await logGeneration(userId, "Direct image transformation", false, undefined, result.error);

      await ctx.reply(
        `❌ Failed to transform image: ${result.error}\n\n` +
        "Please try again or use /generate with a text description."
      );
    }
  } catch (error) {
    console.error(`❌ Error in photo handler for user ${userId}:`, error);
    
    // Log failed generation
    await logGeneration(userId, "Direct image transformation", false, undefined, "Unexpected error");

    await ctx.reply(
      "❌ An unexpected error occurred while transforming your image.\n" +
      "Please try again later or use /generate with a text description."
    );
  }
});

// Handle text messages without commands (provide helpful response)
bot.on("message:text", async (ctx) => {
  // Skip if it's a command
  if (ctx.message?.text?.startsWith("/")) return;
  
  const userId = ctx.from?.id;
  console.log(`💬 Text message from user ${userId}: "${ctx.message?.text}"`);
  
  await ctx.reply(
    "💡 **Tip:** To generate an image, use:\n\n" +
    "• `/generate [your description]` - Create from text\n" +
    "• Send a photo directly - Transform to CryBaby style\n\n" +
    "Example: `/generate a sunset over mountains`\n\n" +
    "Use `/help` for more information."
  );
});

// Error handler
bot.catch((err) => {
  console.error("🚨 Bot error:", err);
});

console.log("✅ Bot commands configured successfully");

// Create webhook callback for Next.js
const handleUpdate = webhookCallback(bot, "std/http");

export async function POST(request: NextRequest) {
  console.log("📨 Webhook POST request received");
  console.log("📋 Request headers:", Object.fromEntries(request.headers.entries()));
  
  try {
    const response = await handleUpdate(request);
    console.log("✅ Webhook processed successfully");
    return response;
  } catch (error) {
    console.error("❌ Webhook error:", error);
    return NextResponse.json({ error: "Webhook processing failed" }, { status: 500 });
  }
}

export async function GET() {
  console.log("🔍 Webhook GET request received (health check)");
  return NextResponse.json({ 
    status: "ok", 
    message: "Telegram webhook endpoint is running",
    timestamp: new Date().toISOString()
  });
}
